from __future__ import annotations

import base64
import json
from typing import List

from openai import OpenAI
import config
from app.content_extractor import ContentExtractor
from config import llm_config
from llm_manager.llm_factory import LLMFactory
from utils.s3_utils import get_s3_path, read_file_from_s3
llm_factory = LLMFactory(llm_config)

client = OpenAI(api_key=config.OPENAI_API_KEY_ADMIN)


def encode_image(image_path):
    # Handle S3 paths
    if image_path.startswith('supload/'):
        # This is an S3 path, use S3 utilities to read it
        full_s3_path = get_s3_path(image_path)
        file_content = read_file_from_s3(full_s3_path)
        if file_content is None:
            raise FileNotFoundError(f"Failed to read S3 file: {image_path}")
        return base64.b64encode(file_content).decode("utf-8")
    else:
        # This is a local path, read it directly
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

def clean_llm_html(raw_html: str) -> str:
    cleaned = raw_html.strip()
    if cleaned.startswith("```html"):
        cleaned = cleaned.replace("```html", "").strip()
    if cleaned.startswith("```"):
        cleaned = cleaned.replace("```", "").strip()
    if cleaned.endswith("```"):
        cleaned = cleaned[:-3].strip()
    for tag in ["html", "head", "body"]:
        cleaned = cleaned.replace(f"<{tag}>", "")
        cleaned = cleaned.replace(f"</{tag}>", "")
    return cleaned


def question_extractor_prompt(page_number: str) -> str:
    extract_questions_prompt = f"You are processing Page {page_number} of a question paper. " \
                               "Extract all visible content from this question paper image and return as clean, semantic HTML. " \
                               "Use valid HTML tags like <h1> to <h6>, <p>, <ol>, <ul>, <table>, etc. to maintain correct formatting and structure. " \
                               "Use heading tags for sections (e.g., 'Instructions to the candidates', question numbers like Q1, Q2, etc.). " \
                               "Use <ol type=\"1\"> for numbered lists and <ol type=\"a\"> for sub-parts. Add spacing and margins between questions to separate clearly. " \
                               "All <table> elements must be wrapped inside <div class='table-wrapper'>...</div>. " \
                               "All mathematical formulas must be converted to valid LaTeX. Use \\( ... \\) for inline and \\[ ... \\] for block math. " \
                               "Ensure the LaTeX is fully syntactically correct for KaTeX — every \\left must have a matching \\right, avoid invalid characters or escapes. " \
                               "Maintain the layout and structure as closely as seen visually in the image — such as indents, question hierarchy, mark allocations, and spacing. " \
                               "Do not explain anything else. Return only the final semantic HTML."
    return extract_questions_prompt


def construct_input_content(prompt: str, image_paths: List[str]) -> list[
    dict[str, str] | dict[str, str | dict[str, str]]]:
    content = [
        {
            "type": "text",
            "text": prompt
        }
    ]

    # Add all images to content
    for path in image_paths:
        base64_image = encode_image(path)
        content.append({
            "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}",
                        }
        })
    return content


def parse_llm_result(response: str) -> str:
    """
    Parse LLM response into structured format

    Args:
        response: Raw LLM response

    Returns:
        Dict containing parsed questions
    """
    try:
        # Clean the response
        if response.startswith("```"):
            response = response[3:]
        elif response.startswith("```json") or response.startswith("```html"):
            response = response[7:]
        if response.endswith("```"):
            response = response[:-3]
        return response
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in LLM response: {str(e)}")
    except Exception as e:
        raise ValueError(f"Failed to parse LLM response: {str(e)}")


def extract_html_from_image(image_path: str, page_number: int = 1) -> str:
    llm = llm_factory.get_llm("openai_extraction", "gpt-4o")
    extractor = ContentExtractor(llm)
    prompt = question_extractor_prompt(str(page_number))
    content = construct_input_content(prompt, [image_path])

    final_validated_html = extractor.extract_content_from_images(
        content,
        parser_func=clean_llm_html
    )

    if isinstance(final_validated_html, str):
        return final_validated_html
    else:
        return str(final_validated_html)
