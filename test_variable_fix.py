#!/usr/bin/env python3
"""
Test script to verify that all variable references are correct in MCQ text extractor.
"""

import os
import sys
import re
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_no_undefined_variables():
    """Test that there are no undefined variable references in the MCQ service."""
    try:
        file_path = "services/mcq_text_extractor_service.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for common undefined variable patterns that we've seen
        problematic_patterns = [
            r's3_upload_result',
            r'batch_json_files\[0\]',  # Should be batch_json_data now
            r'combined_file_path',     # Should be s3_combined_path in some contexts
        ]
        
        issues_found = []
        
        for pattern in problematic_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues_found.append(f"Found potentially problematic pattern '{pattern}': {len(matches)} occurrences")
        
        if issues_found:
            logger.error("❌ Found potential variable issues:")
            for issue in issues_found:
                logger.error(f"  - {issue}")
            return False
        else:
            logger.info("✅ No problematic variable patterns found")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error checking for undefined variables: {e}")
        return False

def test_correct_variable_usage():
    """Test that correct variables are being used."""
    try:
        file_path = "services/mcq_text_extractor_service.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for correct variable usage patterns
        correct_patterns = [
            (r's3_combined_path', "S3 combined path variable"),
            (r'batch_json_data', "Batch JSON data parameter"),
            (r'write_text_to_s3', "Direct S3 text writing function"),
        ]
        
        all_found = True
        
        for pattern, description in correct_patterns:
            matches = re.findall(pattern, content)
            if matches:
                logger.info(f"✅ Found {description}: {len(matches)} occurrences")
            else:
                logger.error(f"❌ Missing {description}")
                all_found = False
        
        return all_found
            
    except Exception as e:
        logger.error(f"❌ Error checking for correct variables: {e}")
        return False

def test_function_signatures():
    """Test that function signatures are consistent."""
    try:
        file_path = "services/mcq_text_extractor_service.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for _combine_json_files signature
        combine_json_pattern = r'async def _combine_json_files\(self, ([^)]+)\)'
        matches = re.findall(combine_json_pattern, content)
        
        if matches:
            signature = matches[0]
            required_params = ['batch_json_data', 'resource_id', 'chapter_id', 'book_id', 'request_id']
            
            missing_params = [param for param in required_params if param not in signature]
            
            if missing_params:
                logger.error(f"❌ Missing parameters in _combine_json_files: {missing_params}")
                return False
            else:
                logger.info(f"✅ _combine_json_files signature looks correct: {signature}")
                return True
        else:
            logger.error("❌ Could not find _combine_json_files function signature")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking function signatures: {e}")
        return False

def main():
    """Run all variable fix tests."""
    logger.info("🚀 Starting variable fix verification tests...")
    
    tests = [
        ("No Undefined Variables", test_no_undefined_variables),
        ("Correct Variable Usage", test_correct_variable_usage),
        ("Function Signatures", test_function_signatures),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All variable fix tests passed!")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
