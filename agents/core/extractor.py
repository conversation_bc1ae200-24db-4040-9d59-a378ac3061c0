import ast
import re
from langchain_core.messages import HumanMessage
from typing import List, Dict, Union, Any, Optional
import os
import logging
import traceback

from app.content_extractor import ContentExtractor
from ..base.agent_base import AgentBase

from config import llm_config
from llm_manager.llm_factory import LLMFactory
from ..utils.image_utils import encode_image_to_base64
from utils.timing_tracker import timing_tracker
from utils.timing_decorators import time_stage

llm_factory = LLMFactory(llm_config)
from ..utils.helpers import calculate_cost

from ..schemas.agent_prompts import (context_prompt, page_classifier_prompt, question_page_classifier_prompt,
                                     explanation_page_classifier_prompt, answer_key_extractor_prompt,
                                     question_extractor_prompt, explanation_extractor_prompt, pre_process_prompt,
                                     pre_process_explanation_prompt, pattern_classifier,
                                     explanation_number_audit_prompt, explanation_recovery_prompt,
                                     extra_fallback_explanation_prompt)

import config
import json
from ..utils.image_extractor import extract_mcq_images
from utils.s3_utils import upload_images_to_s3


logger = logging.getLogger(__name__)

# Set up detailed logging
logging.basicConfig(level=logging.INFO)


class ExtractorAgent(AgentBase):
    """Agent to extract MCQ-related content from images using OpenAI Vision."""

    def __init__(self, workflow_id: Optional[str] = None):
        self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        self.workflow_id = workflow_id

    @time_stage("Process Page Classification")
    async def process(self, image_urls: List[str]) -> Union[Dict[str, str], tuple[Dict, Any]]:
        """Classifies the input image pages for content type."""
        return await self._extract_with_prompt(image_urls, page_classifier_prompt, md_parse=True)

    async def create_questions_metadata(self, image_urls: List[str]) -> Dict:
        return await self._extract_text_metadata(image_urls, question_page_classifier_prompt)

    async def create_explanation_metadata(self, image_urls: List[str]) -> Dict:
        return await self._extract_text_metadata(image_urls, explanation_page_classifier_prompt)

    @time_stage("Extract Answer Keys")
    async def extract_answer_key(self, image_urls: List[str]) -> Union[Dict[str, str], tuple[Dict, int]]:
        return await self._extract_with_prompt(image_urls, answer_key_extractor_prompt(), md_result=True)

    @time_stage("Extract MCQs")
    async def extract_mcqs(self, image_urls: List[str]) -> tuple[Dict, int]:
        try:
            result, cost = await self._extract_with_prompt(image_urls, question_extractor_prompt(), md_result=True)
            if isinstance(result, dict) and 'status' in result and result['status'] == 'error':
                logger.error(f"[MCQ EXTRACTION] Failed with error: {result['message']}")
            return result, cost
        except Exception as e:
            logger.error(f"[MCQ EXTRACTION] Exception occurred: {str(e)}")
            logger.error(f"[MCQ EXTRACTION] Traceback: {traceback.format_exc()}")
            return {"status": "error", "message": str(e)}, 0

    @time_stage("Extract Explanations")
    async def extract_explanation(self, image_urls: List[str]) -> tuple[Dict, int]:
        try:
            result, cost = await self._extract_with_prompt(image_urls, explanation_extractor_prompt(), md_result=True)
            if isinstance(result, dict) and 'status' in result and result['status'] == 'error':
                logger.error(f"[EXPLANATION EXTRACTION] Failed with error: {result['message']}")
            return result, cost
        except Exception as e:
            logger.error(f"[EXPLANATION EXTRACTION] Exception occurred: {str(e)}")
            logger.error(f"[EXPLANATION EXTRACTION] Traceback: {traceback.format_exc()}")
            return {"status": "error", "message": str(e)}, 0

    @time_stage("Pattern Identification")
    async def pattern_identifier(self, image_urls: List[str]) -> Dict:
        content = self.construct_input_content(pattern_classifier, image_urls)
        response = self.llm.invoke([HumanMessage(content=content, additional_kwargs={"tool_choice": "vision"})])
        return self._parse_llm_json(response.content)

    async def _extract_with_prompt(self, image_urls, prompt, md_parse=False, md_result=False, context_info=None, llm_timeout=600):
        self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=llm_timeout)
        prompt_type = "Unknown"
        if "question_extractor" in str(prompt):
            prompt_type = "MCQ"
        elif "explanation_extractor" in str(prompt):
            prompt_type = "Explanation"

        # If context_info is not provided, try to extract it from image_urls
        if context_info is None:
            context_info = {}
            # Try to extract page/column info from the first image URL if available
            if image_urls and len(image_urls) > 0:
                img_path = image_urls[0]
                # Extract page and column from filename if possible
                import re
                page_match = re.search(r'page_([0-9]+)', os.path.basename(img_path))
                col_match = re.search(r'col_([0-9]+)', os.path.basename(img_path))
                if page_match:
                    context_info["page"] = page_match.group(1)
                if col_match:
                    context_info["column"] = col_match.group(1)
                if not context_info:
                    context_info["description"] = os.path.basename(img_path)

        try:
            if self.workflow_id:
                timing_tracker.start_stage(self.workflow_id, f"LLM Call - {prompt[:30]}...")

            content = self.construct_input_content(prompt, image_urls)
            response = ContentExtractor(self.llm).extract_content_from_images(content, None, context_info)

            # Check if this is a special 504 error response
            if hasattr(response, 'additional_kwargs') and response.additional_kwargs.get('skip_due_to_504'):
                # This is a 504 error that couldn't be recovered from
                context = response.additional_kwargs.get('context', '')
                error_details = response.additional_kwargs.get('error_details', 'Unknown error')
                logger.warning(f"[{prompt_type} EXTRACTION] Skipping extraction for {context} due to unrecoverable 504 error: {error_details}")

                # Return a special error result that indicates this extraction was skipped
                return {
                    "status": "skipped_due_to_504",
                    "message": response.content,
                    "context": context,
                    "error_details": error_details
                }, 0

            if self.workflow_id:
                timing_tracker.end_stage(self.workflow_id, f"LLM Call - {prompt[:30]}...")

            if md_parse:
                result = self._parse_llm_md(response.content)
            elif md_result:
                result = self._parse_llm__md_result(response.content)
            else:
                result = response.content

            cost = calculate_cost(response.usage_metadata)

            # ---- EXTRA: Recovery pass for Explanation prompts ----
            if prompt_type == "Explanation":
                total_extra_cost = 0
                audit_prompt = explanation_number_audit_prompt()
                audit_content = self.construct_input_content(audit_prompt, image_urls)
                audit_response = ContentExtractor(self.llm).extract_content_from_images(audit_content, None)
                audit_cost = calculate_cost(audit_response.usage_metadata)
                total_extra_cost += audit_cost
                audit_result = self._parse_llm_json(audit_response.content)
                found = audit_result.get("found", [])
                start = audit_result.get("start")
                end = audit_result.get("end")
                missing = audit_result.get("missing", [])

                if missing:
                    missing_str = ", ".join(map(str, missing))
                    recovery_prompt = explanation_recovery_prompt(missing_str)
                    recovery_content = self.construct_input_content(recovery_prompt, image_urls)
                    recovery_response = ContentExtractor(self.llm).extract_content_from_images(recovery_content, None)
                    recovery_cost = calculate_cost(recovery_response.usage_metadata)
                    total_extra_cost += recovery_cost
                    recovery_text = recovery_response.content.strip()

                    if isinstance(result, str) and isinstance(recovery_text, str):
                        result = self.merge_explanations(result, recovery_text)
                    elif isinstance(result, dict):
                        result["recovery"] = recovery_text  # fallback handling if using dict format

                # ---- EXTRA FALLBACK: Run loose recovery if too many missing ----
                if start is not None and end is not None and len(found) < ((end - start + 1) * 0.6):
                    logger.warning(
                        f"[{prompt_type} EXTRACTION] Too few explanations found ({len(found)} of expected {end - start + 1}). Running loose recovery.")
                    fallback_prompt = extra_fallback_explanation_prompt()
                    fallback_content = self.construct_input_content(fallback_prompt, image_urls)
                    fallback_response = ContentExtractor(self.llm).extract_content_from_images(fallback_content, None)
                    fallback_cost = calculate_cost(fallback_response.usage_metadata)
                    total_extra_cost += fallback_cost
                    fallback_text = fallback_response.content.strip()

                    if isinstance(result, str) and isinstance(fallback_text, str):
                        result = self.merge_explanations(result, fallback_text)
                    elif isinstance(result, dict):
                        result["fallback"] = fallback_text

                cost += total_extra_cost  # Add all extra costs to the main cost

            if isinstance(result, str) and len(result) < 50:
                logger.warning(f"[{prompt_type} EXTRACTION] Result is suspiciously short: '{result}'")

            return result, cost

        except Exception as e:
            if self.workflow_id:
                timing_tracker.end_stage(self.workflow_id, f"LLM Call - {prompt[:30]}...")

            logger.error(f"[{prompt_type} EXTRACTION] Error extracting with prompt: {str(e)}")
            logger.error(f"[{prompt_type} EXTRACTION] Exception traceback: {traceback.format_exc()}")
            return {"status": "error", "message": str(e)}


    async def _extract_text_metadata(self, image_urls, prompt):
        try:
            content = self.construct_input_content(prompt, image_urls)
            response = self.llm.invoke([HumanMessage(content=content)])
            return self._parse_llm_md(response.content)
        except Exception as e:
            logger.error(f"Metadata extraction failed: {str(e)}")
            return {"status": "error", "message": str(e)}

    def extract_quiz_images(self, page_images, book_id, chapter_id, res_id, opt_type, no_parse=False):
        """Extracts and uploads quiz images from pages."""

        output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(res_id), "extracted_mcq_images")
        prompt = pre_process_explanation_prompt if opt_type == "explanation" else pre_process_prompt

        result = []
        print(page_images)
        for page_image in page_images:
            content = self.construct_input_content(prompt, [page_image])
            response = self.llm.invoke([HumanMessage(content=content, additional_kwargs={"tool_choice": "vision"})])
            parsed = None
            mappings = None
            explanation_mappings = None
            print(response.content)
            if no_parse:
                try:
                    # Try JSON load first
                    parsed = json.loads(response.content)
                except json.JSONDecodeError:
                    # If it fails, try ast.literal_eval
                    parsed = ast.literal_eval(response.content)

                print(parsed)
                mappings = parsed.get("mappings")
                explanation_mappings = parsed.get("explanationMappings")
            else:
                parsed = self._parse_llm_json(response.content)
                extract_mcq_images(page_image, parsed, output_dir)

            print(mappings)
            print(explanation_mappings)
            extracted_img_urls = self.extract_quiz_images_new(page_images, book_id, chapter_id, res_id, mappings, explanation_mappings)
            print(extracted_img_urls)
            result.append(extracted_img_urls)
        return result

    def extract_quiz_images_new(self, page_images, book_id, chapter_id, res_id, mappings, explanation_mappings=None):
        """Extracts and uploads quiz images from pages.

        Args:
            page_images: List of page image paths
            book_id: Book ID
            chapter_id: Chapter ID
            res_id: Resource ID
            mappings: Mappings for MCQ images
            explanation_mappings: Mappings for explanation images (optional)

        Returns:
            List of uploaded image URLs
        """

        # Process MCQ mappings - now using S3 instead of local directory
        for page_image in page_images:
            extract_mcq_images(page_image, mappings, book_id, chapter_id, res_id)

        # Process explanation mappings if provided
        if explanation_mappings:
            for page_image in page_images:
                extract_mcq_images(page_image, explanation_mappings, book_id, chapter_id, res_id)

        # Since we're now uploading directly to S3 in extract_mcq_images,
        # we need to get the S3 paths from the uploaded images
        from utils.s3_utils import list_files_in_s3_directory

        # Get all uploaded quiz images from S3
        s3_paths = list_files_in_s3_directory(
            book_id=str(book_id),
            chapter_id=str(chapter_id),
            res_id=str(res_id),
            subfolder="extractedQuizImages",
            file_pattern="*.png"
        )

        return s3_paths

    @staticmethod
    def construct_input_content(prompt_type: str, image_urls: List[str]) -> List[Dict[str, Union[str, Dict[str, str]]]]:
        content = [{"type": "text", "text": f"Context:\n{context_prompt}\n{prompt_type}"}]
        for url in image_urls:
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(url)}",
                    "detail": "high"
                }
            })
        return content

    @staticmethod
    def get_image_paths(folder_path):
        if not os.path.isdir(folder_path):
            return []

        return [os.path.join(folder_path, f).replace(os.sep, '/')
                for f in os.listdir(folder_path)
                if os.path.isfile(os.path.join(folder_path, f)) and f.lower().endswith(('.png', '.jpg', '.jpeg'))]

    @staticmethod
    def upload_images_to_ws_server(image_paths, book_id, chapter_id, res_id):
        # Use direct S3 upload instead of API call
        # Filter out non-existent files
        valid_image_paths = [p for p in image_paths if os.path.isfile(p)]

        if not valid_image_paths:
            return []

        try:
            # Upload images directly to S3
            s3_paths = upload_images_to_s3(
                valid_image_paths,
                book_id,
                chapter_id,
                res_id,
                is_quiz_images=True
            )

            return s3_paths
        except Exception as e:
            logger.error(f"[ExtractorAgent] S3 upload failed: {e}")
            return []

    @staticmethod
    def map_images_to_mcqs(mcq_data, extracted_urls, subtype):
        if not mcq_data:
            return []

        if not extracted_urls:
            return mcq_data

        try:
            data = mcq_data.copy()
            url_map = {url.split('/')[-1].split('.')[0]: url for url in extracted_urls}

            for idx, item in enumerate(data):
                # Check if the item is a dictionary
                if not isinstance(item, dict):
                    continue

                key = item.get('question_number') if subtype == 'mcq' else item.get('explanation_number')
                if key is None:
                    # Instead of adding a fallback, we'll skip this item since it won't match any images
                    continue

                # Ensure key is an integer if it's not already
                # This is critical for compatibility with the JavaScript code in web/static/fixed_display_results.js
                # which expects question_number to be a number, not a string
                try:
                    if not isinstance(key, int):
                        key = int(key)
                        if subtype == 'mcq':
                            item['question_number'] = key
                        else:
                            item['explanation_number'] = key
                except (ValueError, TypeError) as e:
                    logger.warning(f"[IMAGE MAPPING] Could not convert key to integer: {key}, error: {e}")
                    # Skip this item since we can't determine a valid key
                    # This is important because the JavaScript code in web/static/fixed_display_results.js
                    # performs arithmetic operations on question_number (sorting, etc.)
                    continue

                base_key = f"question_{key}" if subtype == 'mcq' else f"explanation_{key}"
                image_field = 'question_images' if subtype == 'mcq' else 'explanation_images'

                if base_key in url_map:
                    item[image_field] = [url_map[base_key]]

                if subtype == 'mcq':
                    option_images = {}
                    for i in range(1, 5):
                        option_key = f"{base_key}_option_{i}"
                        if option_key in url_map:
                            option_images[f"option_{i}"] = [url_map[option_key]]

                    if option_images:
                        item['option_images'] = option_images

            return data
        except Exception as e:
            logger.error(f"[IMAGE MAPPING] Error during image mapping: {str(e)}")
            logger.error(f"[IMAGE MAPPING] Traceback: {traceback.format_exc()}")

            # Add a special error flag to the data to indicate there was a problem
            # This can be used for debugging but won't break the flow
            if isinstance(mcq_data, list):
                return mcq_data
            elif isinstance(mcq_data, dict):
                return mcq_data
            else:
                # Return an empty list as a last resort to avoid breaking the flow
                return []

    import re

    def merge_explanations(self, full: str, recovery: str) -> str:
        """
        Merge full and recovery explanation results by avoiding duplicates and keeping correct order.
        Assumes each explanation starts with a pattern like: <number>. (<option>)
        """

        def extract_expl_blocks(text):
            """Extract explanation blocks using regex. Returns a dict: {number: block}"""
            blocks = {}
            pattern = re.compile(r"(\\d{1,3})\\.\\s*\\(\\d\\).*?(?=\\n\\d{1,3}\\.\\s*\\(\\d\\)|\\Z)", re.DOTALL)
            for match in pattern.finditer(text):
                number = int(match.group(1))
                blocks[number] = match.group(0).strip()
            return blocks

        # Extract explanation blocks from both full and recovery
        full_blocks = extract_expl_blocks(full)
        recovery_blocks = extract_expl_blocks(recovery)

        # Merge: prefer full_blocks, but fill missing from recovery
        merged_blocks = full_blocks.copy()
        for num, block in recovery_blocks.items():
            if num not in merged_blocks:
                merged_blocks[num] = block

        # Return ordered string by explanation number
        merged_text = "\n\n".join(
            block for _, block in sorted(merged_blocks.items())
        )
        return merged_text


    def compare_keys(self, answer_keys, explanation_keys, max_count):
        answer_dict = self.parse_answer_keys(answer_keys)
        explanation_dict = self.parse_explanation_keys(explanation_keys)
        result = []

        for i in range(1, max(max(answer_dict.keys(), default=0), max(explanation_dict.keys(), default=0), int(max_count)) + 1):
            a_key = answer_dict.get(i, '')
            e_key = explanation_dict.get(i, '')
            result.append({
                "question": i,
                "correct_ans_key": a_key,
                "explanation_key": e_key,
                "valid": e_key and e_key.upper() == a_key.upper()
            })
        return result

    @staticmethod
    def parse_answer_keys(s):
        result = {}
        for line in s.strip().split('\n'):
            match = re.search(r'(?:\(?)?(\d+)(?:\))?\s*(?:\(?)?([0-9A-Za-z]+)(?:\))?', line.strip())
            if match:
                num, val = match.groups()
                if len(val) == 1 and val.upper() in 'ABCDE':
                    val = str(ord(val.upper()) - ord('A') + 1)
                result[int(num)] = val
        return result

    @staticmethod
    def parse_explanation_keys(data: Union[str, List[str]]) -> dict:
        if isinstance(data, list):
            data = '\n'.join(data)
        if not isinstance(data, str):
            raise ValueError("Input must be string or list of strings")

        pattern = r'(\d+)\.\s*\(([0-9A-Za-z]+)\)'
        result = {}
        for m in re.finditer(pattern, data):
            qn, val = int(m.group(1)), m.group(2)
            if val.upper() in 'ABCDE':
                val = str(ord(val.upper()) - ord('A') + 1)
            result[qn] = val
        return result

    @staticmethod
    def _parse_llm_md(text: str) -> Dict:
        if "```markdown" in text:
            start = text.find("```markdown") + len("```markdown")
            end = text.rfind("```")
            text = text[start:end].strip()

        content_classification_data = {}

        for line in text.strip().split('\n'):
            pairs = line.split(',')

            for pair in pairs:
                if ':' in pair:
                    k, v = pair.split(':', 1)
                    v = v.strip().strip('"')

                    if v.isdigit():
                        content_classification_data[k.strip()] = int(v)
                    elif v.lower() in ['true', 'false']:
                        content_classification_data[k.strip()] = v.lower() == 'true'
                    else:
                        content_classification_data[k.strip()] = v

        return content_classification_data

    @staticmethod
    def _parse_llm__md_result(response: str) -> Dict:
        """
        Parse LLM response into structured format

        Args:
            response: Raw LLM response

        Returns:
            Dict containing parsed questions
        """
        try:
            # Clean the response
            if response.startswith("```"):
                response = response[3:]
            elif response.startswith("```json"):
                response = response[7:]
            if response.endswith("```"):
                response = response[:-3]
            return response
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in LLM response: {str(e)}")
        except Exception as e:
            raise ValueError(f"Failed to parse LLM response: {str(e)}")

    @staticmethod
    def _parse_llm_json(resp: str) -> Dict:
        match = re.search(r'```(?:json)?\s*(.*?)```', resp, re.DOTALL)
        try:
            json_str = match.group(1).strip() if match else resp
            json_str = json_str.replace('None', 'null')
            result = json.loads(json_str)
            return result
        except json.JSONDecodeError as e:
            logger.error(f"[PARSER] Invalid JSON in LLM response: {str(e)}")
            logger.error(f"[PARSER] Problematic JSON string: {json_str[:200]}...")
            raise ValueError(f"Invalid JSON in LLM response: {str(e)}")
        except Exception as e:
            logger.error(f"[PARSER] Failed to parse LLM response: {str(e)}")
            logger.error(f"[PARSER] Exception traceback: {traceback.format_exc()}")
            raise ValueError(f"Failed to parse LLM response: {str(e)}")
