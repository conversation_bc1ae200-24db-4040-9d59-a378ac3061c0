from typing import List, Dict, Optional, cast

import cv2
import numpy as np
import os
import logging
import tempfile
from utils.s3_utils import upload_file_to_s3


logger = logging.getLogger(__name__)


def extract_mcq_images(image_path, mappings, book_id=None, chapter_id=None, res_id=None, output_dir=None):
    """
    Extract MCQ images using a fully dynamic approach and upload to S3

    Args:
        image_path (str): Path to the image file
        mappings ([]): JSON data with mapping information
        book_id (str): Book identifier for S3 upload
        chapter_id (str): Chapter identifier for S3 upload
        res_id (str): Resource identifier for S3 upload
        output_dir (str, optional): Legacy parameter, not used when uploading to S3

    Returns:
        dict: Dictionary of extracted images with S3 paths
    """
    # For backward compatibility, create a temp directory if no S3 params provided
    use_s3 = book_id is not None and chapter_id is not None and res_id is not None

    if not use_s3 and output_dir is None:
        output_dir = "extracted_mcq_images"

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create a temporary directory for intermediate files
    temp_dir = os.path.join(output_dir, "temp")
    os.makedirs(temp_dir, exist_ok=True)

    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        logger.error(f"Could not read image: {image_path}")
        return {}

    # Get image dimensions
    height, width = image.shape[:2]

    # Get mappings from JSON
    mappings = mappings or []

    # Create a dictionary to organize mappings by question
    question_map = {}
    explanation_map = {}

    for idx, item in enumerate(mappings):
        try:
            if not item:
                continue

            category = item.get("category")
            if not category:
                continue

            if category in ("question", "option"):
                question_num = item.get("question_number")
                if question_num is None:
                    continue

                if question_num not in question_map:
                    question_map[question_num] = {"question": False, "options": set()}

                if category == "question":
                    question_map[question_num]["question"] = True
                elif category == "option":
                    option_number = item.get("option_number")
                    if option_number is not None:
                        question_map[question_num]["options"].add(option_number)

            elif category == "explanation":
                explanation_num = item.get("explanation_number")
                if explanation_num is None:
                    continue

                if explanation_num not in explanation_map:
                    explanation_map[explanation_num] = True

        except Exception as e:
            logger.error(f"Error processing item at index {idx}: {e} - Item: {item}")
            continue

    # Detect all potential regions in the image
    regions = detect_regions(image, temp_dir)

    # Create visualization image
    vis_image = image.copy()

    # Draw detected regions on visualization
    for i, region in enumerate(regions):
        x, y, w, h = region["bbox"]
        cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(vis_image, f"{i}", (x, y - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    # Save visualization
    vis_path = os.path.join(temp_dir, "detected_regions.jpg")
    cv2.imwrite(vis_path, vis_image)

    # Map the detected regions to questions and options
    mapped_regions = map_regions_to_mappings(image, regions, question_map, explanation_map)

    # Process each mapped region
    processed_images = {}
    for key, region_data in mapped_regions.items():
        try:
            x, y, w, h = region_data["bbox"]

            # Extract the region
            region_img = image[y:y + h, x:x + w].copy()

            if use_s3:
                # Save to temporary file and upload to S3
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                    cv2.imwrite(temp_file.name, region_img)

                    # Upload to S3
                    s3_path = upload_file_to_s3(
                        temp_file.name,
                        str(book_id),
                        str(chapter_id),
                        str(res_id),
                        file_name=f"{key}.png",
                        is_quiz_image=True  # These are extracted quiz images
                    )

                    # Clean up temporary file
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass

                    # Add to results with S3 path
                    processed_images[key] = {
                        "path": s3_path if s3_path else None,
                        "bbox": [x, y, x + w, y + h]
                    }
            else:
                # Legacy local file saving
                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)
                    region_path = os.path.join(output_dir, f"{key}.png")
                    cv2.imwrite(region_path, region_img)

                    # Add to results
                    processed_images[key] = {
                        "path": region_path,
                        "bbox": [x, y, x + w, y + h]
                    }

        except Exception as e:
            logger.error(f"Error processing {key}: {e}")

    # Create a visualization of the mapped regions
    vis_mapped = image.copy()
    for key, data in mapped_regions.items():
        x, y, w, h = data["bbox"]
        if "question" in key and "option" not in key:
            color = (0, 0, 255)  # Red for questions
        else:
            color = (255, 0, 0)  # Blue for options
        cv2.rectangle(vis_mapped, (x, y), (x + w, y + h), color, 2)
        cv2.putText(vis_mapped, key, (x, y - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

    # Save visualization
    vis_mapped_path = os.path.join(temp_dir, "mapped_regions.jpg")
    cv2.imwrite(vis_mapped_path, vis_mapped)

    return processed_images


def prepare_temp_dir(output_dir):
    temp_dir = os.path.join(output_dir, "temp")
    os.makedirs(temp_dir, exist_ok=True)
    return temp_dir


def load_image(path):
    image = cv2.imread(path)
    if image is None:
        logger.error(f"Could not read image: {path}")
    return image


def organize_mappings(json_data):
    question_map, explanation_map = {}, {}
    for item in json_data.get("mappings", []):
        category = item.get("category")
        if category in ["question", "option"]:
            qn = item["question_number"]
            question_map.setdefault(qn, {"question": False, "options": set()})
            if category == "question":
                question_map[qn]["question"] = True
            elif item.get("option_number") is not None:
                question_map[qn]["options"].add(item["option_number"])
        elif category == "explanation":
            explanation_map[item["explanation_number"]] = True

    return question_map, explanation_map


def save_region_visualization(image, regions, temp_dir):
    vis_image = image.copy()
    for i, r in enumerate(regions):
        x, y, w, h = r["bbox"]
        cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(vis_image, str(i), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    vis_path = os.path.join(temp_dir, "detected_regions.jpg")
    cv2.imwrite(vis_path, vis_image)
    return vis_path


def extract_and_save_regions(image, mapped_regions, output_dir):
    processed_images = {}
    for key, data in mapped_regions.items():
        try:
            x, y, w, h = data["bbox"]
            region_img = image[y:y + h, x:x + w].copy()
            save_path = os.path.join(output_dir, f"{key}.png")
            cv2.imwrite(save_path, region_img)
            processed_images[key] = {"path": save_path, "bbox": [x, y, x + w, y + h]}
        except Exception as e:
            logger.error(f"Error processing {key}: {e}")
    return processed_images


def save_mapped_region_visualization(image, mapped_regions, temp_dir):
    vis_image = image.copy()
    for key, data in mapped_regions.items():
        x, y, w, h = data["bbox"]
        color = (0, 0, 255) if "question" in key and "option" not in key else (255, 0, 0)
        cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 2)
        cv2.putText(vis_image, key, (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    vis_path = os.path.join(temp_dir, "mapped_regions.jpg")
    cv2.imwrite(vis_path, vis_image)


def log_extraction_summary(processed_images):
    pass  # Removed logging for cleaner output


def detect_regions(image, output_dir):
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 210, 255, cv2.THRESH_BINARY_INV)
    save_debug_image(binary, output_dir, "binary.jpg")

    binary = apply_morphology(binary)
    edges = cv2.Canny(binary, 50, 150)
    save_debug_image(edges, output_dir, "edges.jpg")

    edges = cv2.dilate(edges, np.ones((3, 3), np.uint8), iterations=1)
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    return extract_region_props(contours)


def apply_morphology(image):
    kernel = np.ones((3, 3), np.uint8)
    return cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel)


def extract_region_props(contours, min_size=30):
    regions = []
    for i, contour in enumerate(contours):
        x, y, w, h = cv2.boundingRect(contour)
        if w < min_size or h < min_size:
            continue
        area = w * h
        aspect_ratio = w / h if h > 0 else 0
        regions.append({
            "id": i,
            "bbox": (x, y, w, h),
            "area": area,
            "aspect_ratio": aspect_ratio,
            "center_x": x + w // 2,
            "center_y": y + h // 2
        })
    return sorted(regions, key=lambda r: (r["center_y"], r["center_x"]))


def save_debug_image(image: np.ndarray, output_dir: str, filename: str) -> None:
    """
    Save a debug image to the specified output directory with the given filename.

    Args:
        image (np.ndarray): The image to save.
        output_dir (str): The output directory.
        filename (str): The name of the image file to be saved.
    """
    path = os.path.join(output_dir, filename)
    cv2.imwrite(path, image)


def safe_sort_question_map_items(question_map: Dict) -> List:
    """
    Safely sort question_map items handling mixed data types (int and str).
    Special question numbers like '__prev__' are placed at the end.
    """
    try:
        # Separate numeric and non-numeric question numbers
        numeric_items = []
        non_numeric_items = []

        for question_num, details in question_map.items():
            try:
                # Try to convert to int for proper numeric sorting
                if isinstance(question_num, str) and question_num.isdigit():
                    numeric_items.append((int(question_num), details))
                elif isinstance(question_num, int):
                    numeric_items.append((question_num, details))
                else:
                    # Non-numeric items like '__prev__'
                    non_numeric_items.append((question_num, details))
            except (ValueError, TypeError):
                non_numeric_items.append((question_num, details))

        # Sort numeric items by their numeric value
        numeric_items.sort(key=lambda x: x[0])

        # Sort non-numeric items alphabetically
        non_numeric_items.sort(key=lambda x: str(x[0]))

        # Combine: numeric first, then non-numeric
        return numeric_items + non_numeric_items

    except Exception as e:
        logger.error(f"Error sorting question_map items: {e}")
        # Fallback: return items as-is without sorting
        return list(question_map.items())


def map_regions_to_mappings(image, regions: List[Dict], question_map: Optional[Dict] = None,
                             explanation_map: Optional[Dict] = None) -> Dict[str, Dict]:
    _, width = image.shape[:2]
    mapped_regions = {}
    used_region_ids = set()
    explanation_map = explanation_map or {}

    left_regions, right_regions = split_and_sort_regions(regions, width)

    if question_map:
        try:
            # Use safe sorting to handle mixed data types
            sorted_question_items = safe_sort_question_map_items(question_map)

            for question_num, details in sorted_question_items:
                if not details["question"]:
                    continue

                question_key = f"question_{question_num}"
                question_candidates = get_question_candidates(right_regions, left_regions, used_region_ids)

                if question_candidates:
                    mapped_regions, used_region_ids = assign_question_region(
                        mapped_regions, used_region_ids, question_key, question_candidates[0]
                    )

                    if details["options"]:
                        mapped_regions, used_region_ids = assign_option_regions(
                            mapped_regions, used_region_ids, regions,
                            question_num, details["options"], question_candidates[0]["bbox"]
                        )
        except Exception as e:
            logger.error(f"Error processing question_map in map_regions_to_mappings: {e}")
            # Continue without question mapping if there's an error

    if explanation_map:
        try:
            mapped_regions = assign_explanation_regions(mapped_regions, regions, explanation_map, used_region_ids)
        except Exception as e:
            logger.error(f"Error processing explanation_map in map_regions_to_mappings: {e}")
            # Continue without explanation mapping if there's an error

    return mapped_regions

def split_and_sort_regions(regions: List[Dict], width: int):
    left = sorted([r for r in regions if r["center_x"] < width / 2], key=lambda r: r["center_y"])
    right = sorted([r for r in regions if r["center_x"] >= width / 2], key=lambda r: r["center_y"])
    return left, right

def get_question_candidates(right, left, used_ids):
    candidates = [r for r in right if r["id"] not in used_ids and r["area"] > 5000 and 0.8 < r["aspect_ratio"] < 2.0]
    if not candidates:
        candidates = [r for r in left if r["id"] not in used_ids and r["area"] > 5000]
    return sorted(candidates, key=lambda r: r["area"], reverse=True)

def assign_question_region(mapped, used_ids, q_key: str, region: Dict):
    mapped[q_key] = {"bbox": region["bbox"], "id": region["id"]}
    used_ids.add(region["id"])
    return mapped, used_ids

def assign_option_regions(mapped, used_ids, regions, q_num, option_nums, q_bbox):
    q_x, q_y, q_w, q_h = q_bbox
    candidates = []
    for region in regions:
        if region["id"] in used_ids:
            continue
        r_x, r_y, r_w, _ = region["bbox"]
        if r_y > (q_y + q_h) and abs(r_x - q_x) < q_w:
            v_dist = r_y - (q_y + q_h)
            h_offset = abs(r_x + r_w / 2 - (q_x + q_w / 2))
            score = v_dist + 2 * h_offset
            candidates.append((region, score))

    candidates.sort(key=lambda x: x[1])
    sorted_opts = sorted([o for o in option_nums if o is not None])

    for i, opt in enumerate(sorted_opts):
        if i >= len(candidates):
            break
        reg = candidates[i][0]
        opt_key = f"question_{q_num}_option_{opt}"
        mapped[opt_key] = {"bbox": reg["bbox"], "id": reg["id"]}
        used_ids.add(reg["id"])

    return mapped, used_ids

def safe_sort_explanation_keys(exp_map: Dict) -> List:
    """
    Safely sort explanation map keys handling mixed data types (int and str).
    """
    try:
        # Separate numeric and non-numeric keys
        numeric_keys = []
        non_numeric_keys = []

        for key in exp_map.keys():
            try:
                # Try to convert to int for proper numeric sorting
                if isinstance(key, str) and key.isdigit():
                    numeric_keys.append(int(key))
                elif isinstance(key, int):
                    numeric_keys.append(key)
                else:
                    # Non-numeric keys
                    non_numeric_keys.append(key)
            except (ValueError, TypeError):
                non_numeric_keys.append(key)

        # Sort numeric keys by their numeric value
        numeric_keys.sort()

        # Sort non-numeric keys alphabetically
        non_numeric_keys.sort(key=str)

        # Combine: numeric first, then non-numeric
        return numeric_keys + non_numeric_keys

    except Exception as e:
        logger.error(f"Error sorting explanation map keys: {e}")
        # Fallback: return keys as-is without sorting
        return list(exp_map.keys())


def assign_explanation_regions(mapped, regions, exp_map, used_ids):
    remaining = [r for r in regions if r["id"] not in used_ids and r["area"] > 7000]
    remaining.sort(key=lambda r: r["center_y"])

    try:
        # Use safe sorting to handle mixed data types
        sorted_exp_keys = safe_sort_explanation_keys(exp_map)

        for i, exp_num in enumerate(sorted_exp_keys):
            if i >= len(remaining):
                continue
            region = remaining[i]
            key = f"explanation_{exp_num}"
            mapped[key] = {"bbox": region["bbox"], "id": region["id"]}
            used_ids.add(region["id"])
    except Exception as e:
        logger.error(f"Error in assign_explanation_regions: {e}")
        # Continue without explanation assignment if there's an error

    return mapped


def clean_and_center_diagram(img):
    """
    Clean up a diagram image by removing noise, cropping the content, and centering it in a square.

    Args:
        img: Input image (color or grayscale)

    Returns:
        np.ndarray: Cleaned and centered image.
    """
    if is_empty_image(img):
        return create_blank_square((200, 200), color=True)

    img_copy = img.copy()
    gray = cv2.cvtColor(img_copy, cv2.COLOR_BGR2GRAY) if len(img_copy.shape) == 3 else img_copy.copy()
    binary = threshold_and_clean(gray)

    if np.sum(binary) < 100:
        return center_image_in_square(img_copy)

    content_mask = extract_clean_mask(binary)

    coords = np.column_stack(np.nonzero(content_mask > 0))
    if coords.size == 0:
        return center_image_in_square(img_copy)

    cropped = crop_to_bounding_box(img_copy, coords)
    if cropped.size == 0 or cropped.shape[0] < 10 or cropped.shape[1] < 10:
        return img_copy

    return center_image_in_square(cropped)


def is_empty_image(img):
    return img is None or img.size == 0


def create_blank_square(shape, color=False):
    h, w = shape
    channels = 3 if color else 1
    return np.ones((h, w, channels), dtype=np.uint8) * 255


def threshold_and_clean(gray_img):
    _, binary = cv2.threshold(gray_img, 200, 255, cv2.THRESH_BINARY_INV)
    kernel = np.ones((2, 2), np.uint8)
    return cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)


def extract_clean_mask(binary_img: np.ndarray) -> np.ndarray:
    """
    Extracts a clean binary mask by filtering contours based on area.

    Args:
        binary_img (np.ndarray): Binary input image.

    Returns:
        np.ndarray: Cleaned binary mask with selected contours filled.
    """
    mask = np.zeros_like(binary_img, dtype=np.uint8)
    contours, _ = cv2.findContours(binary_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        area = cv2.contourArea(contour)
        if 20 < area < 0.9 * binary_img.shape[0] * binary_img.shape[1]:
            safe_contours = cast(List[np.ndarray], [contour])
            cv2.drawContours(mask, safe_contours, -1, 255, thickness=cv2.FILLED)

    return mask


def crop_to_bounding_box(img, coords, padding=10):
    y_min, x_min = coords.min(axis=0)
    y_max, x_max = coords.max(axis=0)

    y_min = max(0, y_min - padding)
    y_max = min(img.shape[0], y_max + padding)
    x_min = max(0, x_min - padding)
    x_max = min(img.shape[1], x_max + padding)

    if y_min >= y_max or x_min >= x_max:
        return img

    return img[y_min:y_max, x_min:x_max]


def center_image_in_square(img, padding=10):
    h, w = img.shape[:2]
    size = max(h, w) + 2 * padding
    color = (len(img.shape) == 3)
    square = create_blank_square((size, size), color=color)

    y_offset = (size - h) // 2
    x_offset = (size - w) // 2
    square[y_offset:y_offset + h, x_offset:x_offset + w] = img

    return square


def enhance_diagram(img):
    """
    Enhance a diagram image for better visibility using adaptive thresholding.

    Args:
        img: Input image (grayscale or color)

    Returns:
        np.ndarray: Enhanced binary image for better visual clarity.
    """
    if is_empty_image(img):
        return create_blank_square((200, 200))

    gray = convert_to_grayscale(img)

    try:
        return adaptive_threshold(gray)
    except Exception as e:
        logger.error(f"Error during enhancement: {e}")
        return gray


def convert_to_grayscale(img):
    return cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if len(img.shape) == 3 else img.copy()


def adaptive_threshold(gray_img):
    return cv2.adaptiveThreshold(
        gray_img,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,
        2
    )
